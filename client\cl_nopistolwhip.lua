----------------------------------------------------------------------------
--DANO CORONHADA //// PISTOL WHIPPING
----------------------------------------------------------------------------
-- OPTIMIZED: Reduced from Wait(0) to Wait(50) and cached PlayerPedId()
local lastPedCheck = 0
local cachedPed = 0

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(50) -- Optimized: Was 0, now 50ms - pistol whip check doesn't need frame-perfect timing

        -- Cache PlayerPedId() to avoid repeated native calls
        local currentTime = GetGameTimer()
        if currentTime - lastPedCheck > 100 then -- Update ped reference every 100ms
            cachedPed = PlayerPedId()
            lastPedCheck = currentTime
        end

        if IsPedArmed(cachedPed, 6) then
           DisableControlAction(1, 140, true)
           DisableControlAction(1, 141, true)
           DisableControlAction(1, 142, true)
        end
    end
end)

-- REMOVED: Duplicate NPC density code - already handled in cl_nonpcs.lua
-- This was causing the same operations to run twice, wasting CPU cycles
