
--[[

	Holograms / Floating text Script by <PERSON><PERSON>
	
	Just put in the coordinates you get when standing on the ground, it's above the ground then
	By default, you only see the hologram when you are within 10m of it, to change that, edit the 10.0 infront of the "then"
	The Default holograms are at the Observatory.
	
	If you want to add a line to the hologram, just make a new Draw3DText line with the same coordinates, and edit the vertical offset.
	
	Formatting:
			Draw3DText( x, y, z  vertical offset, "text", font, text size, text size)
			
			
	To add a new hologram, copy paste this example under the existing ones, and put coordinates and text into it.
	
		if GetDistanceBetweenCoords( X, Y, Z, GetEntityCoords(GetPlayerPed(-1))) < 10.0 then
			Draw3DText( X, Y, Z,  -1.400, "TEXT", 4, 0.1, 0.1)
			Draw3DText( X, Y, Z,  -1.600, "TEXT", 4, 0.1, 0.1)
			Draw3DText( X, Y, Z,  -1.800, "TEXT", 4, 0.1, 0.1)		
		end


]]--

Citizen.CreateThread(function()
    Holograms()
end)

-- OPTIMIZED: Improved performance with distance-based wait times and cached coordinates
function Holograms()
    local cityHallCoords = vector3(-534.1622, -222.9581, 37.6498)
    local lastPlayerCoords = vector3(0, 0, 0)
    local lastDistanceCheck = 0
    local isNearCityHall = false

    while true do
        local currentTime = GetGameTimer()
        local waitTime = 500 -- Default wait when far away

        -- Only check distance every 500ms to reduce native calls
        if currentTime - lastDistanceCheck > 500 then
            local playerCoords = GetEntityCoords(PlayerPedId())
            local distance = #(cityHallCoords - playerCoords)
            isNearCityHall = distance < 23.0
            lastDistanceCheck = currentTime
            lastPlayerCoords = playerCoords
        end

        if isNearCityHall then
            waitTime = 100 -- Faster updates when near the hologram

            -- City Hall Commands Display
            Draw3DText(cityHallCoords.x, cityHallCoords.y, cityHallCoords.z - 0.600, "X", 7, 0.1, 0.1)
            Draw3DText(cityHallCoords.x, cityHallCoords.y, cityHallCoords.z - 0.800, "~q~Commands!", 7, 0.1, 0.1)
            Draw3DText(cityHallCoords.x, cityHallCoords.y, cityHallCoords.z - 1.000, "F6 - Teleport Menu", 4, 0.1, 0.1)
            Draw3DText(cityHallCoords.x, cityHallCoords.y, cityHallCoords.z - 1.200, "/duels- 1v1 or 5v5", 4, 0.1, 0.1)
            Draw3DText(cityHallCoords.x, cityHallCoords.y, cityHallCoords.z - 1.400, "/Hud - Change color the hud", 4, 0.1, 0.1)
            Draw3DText(cityHallCoords.x, cityHallCoords.y, cityHallCoords.z - 1.600, "/cleanup {Radius} - to force delete vehicles", 4, 0.1, 0.1)
            Draw3DText(cityHallCoords.x, cityHallCoords.y, cityHallCoords.z - 1.800, "/Supp /Extended /Grip /Scope - Add Attachments on Addon Weapons", 4, 0.1, 0.1)
            Draw3DText(cityHallCoords.x, cityHallCoords.y, cityHallCoords.z - 2.000, "/Lobby - Private Lobbies - Use For Tracking", 4, 0.1, 0.1)
            Draw3DText(cityHallCoords.x, cityHallCoords.y, cityHallCoords.z - 2.200, "/skin - Custom Character Menu", 4, 0.1, 0.1)
        end

        Citizen.Wait(waitTime) -- Dynamic wait: 100ms when near, 500ms when far
    end
end

-------------------------------------------------------------------------------------------------------------------------
function Draw3DText(x,y,z,textInput,fontId,scaleX,scaleY)
         local px,py,pz=table.unpack(GetGameplayCamCoords())
         local dist = GetDistanceBetweenCoords(px,py,pz, x,y,z, 1)    
         local scale = (1/dist)*20
         local fov = (1/GetGameplayCamFov())*100
         local scale = scale*fov   
         SetTextScale(scaleX*scale, scaleY*scale)
         SetTextFont(fontId)
         SetTextProportional(1)
         SetTextColour(250, 250, 250, 255)		-- You can change the text color here
         SetTextDropshadow(1, 1, 1, 1, 255)
         SetTextEdge(2, 0, 0, 0, 150)
         SetTextDropShadow()
         SetTextOutline()
         SetTextEntry("STRING")
         SetTextCentre(1)
         AddTextComponentString(textInput)
         SetDrawOrigin(x,y,z+2, 0)
         DrawText(0.0, 0.0)
         ClearDrawOrigin()
        end
