-- HUD Management - OPTIMIZED for better performance

-- OPTIMIZED: Consolidated both threads into one and reduced wait time
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(100) -- Optimized: Was 0, now 100ms - HUD updates don't need frame-perfect timing

        -- Disable Money HUD
        DisplayCash(false)
        HideHudComponentThisFrame(3)  -- Cash
        HideHudComponentThisFrame(4)  -- MP Cash

        -- Hide death messages (native GTA death feed)
        HideHudComponentThisFrame(17)

        -- Disable Police Scanner / Caller
        DisablePoliceReports()

        -- NOTE: We are NOT disabling dispatch services so PD can still respond
        -- Removed: EnableDispatchService(i, false)
    end
end)
