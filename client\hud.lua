local hud = config.setDefaultHud()

local lastStreetArea = ""
local hudHidden = false
local adminMap = false
local lastSpeed = 0

-- Disable automatic health regeneration
Citizen.CreateThread(function()
    while true do
        SetPlayerHealthRechargeMultiplier(PlayerId(), 0.0)
        Citizen.Wait(1000) -- Reapply every second to ensure it's always off
    end
end)

Citizen.CreateThread(function()
    while true do
        local ped = PlayerPedId()
        local pos = GetEntityCoords(ped)

        -- Street labels
        local str1, str2 = GetStreetNameAtCoord(pos.x, pos.y, pos.z)
        local streetLabel = "| " .. GetStreetNameFromHashKey(str1)
        local streetArea = config.zones[GetNameOfZone(pos.x, pos.y, pos.z)] or lastStreetArea
        local time = "00:00"
        lastStreetArea = streetArea

        if (str2 ~= 0) then
            streetLabel = streetLabel .. " & " .. GetStreetNameFromHashKey(str2)
        end

        -- Clock time
        if (GetClockHours() < 10) then
            time = "0" .. GetClockHours()
        else
            time = GetClockHours()
        end

        if (GetClockMinutes() < 10) then
            time = time .. ":0" .. GetClockMinutes()
        else
            time = time .. ":" .. GetClockMinutes()
        end

        -- Top bar with street names
        hud.location.data_1 = streetLabel

        -- Bottom bar with area | postcode | time
        hud.location.data_2 = streetArea .. " | " .. time

        -- Sends in raw heading in a float
        hud.location.direction = math.floor(GetEntityHeading(ped))

        if hudHidden then
            hud.location.data_1 = ""
            hud.location.data_2 = ""
            hud.location.direction = ""
        end

        Wait(1000)
    end
end)

Citizen.CreateThread(function()
    while true do
        local ped = PlayerPedId()

        hud.id = "hud"
        hud.hide = hudHidden

        hud.health = math.floor(GetEntityHealth(ped) - 100)
        hud.armour = math.floor(GetPedArmour(ped))

        if IsPedSwimmingUnderWater(ped) then
            hud.oxy = GetPlayerUnderwaterTimeRemaining(PlayerId()) * 2.5
        else
            hud.oxy = 0
        end

        hud.voice.active = NetworkIsPlayerTalking(PlayerId())

        -- Fixed health bar behavior:
        if isDead then
            hud.health = 0
        elseif hud.health > 100 then
            hud.health = 100
        end

        if (IsPauseMenuActive()) then
            hud.hide = true
        end

        hud.vehicle.inVehicle = IsPedInAnyVehicle(ped, false)
        if (hud.hide) then
            hud.vehicle.inVehicle = false
        end

        if (hud.vehicle.inVehicle) then
            local veh = GetVehiclePedIsIn(ped, false)
            local currentSpeed = GetEntitySpeed(veh) * 3.6
            lastSpeed = lastSpeed + (currentSpeed - lastSpeed) * 0.2 -- Smooth transition

            hud.vehicle.speed = math.floor(lastSpeed + 0.5) -- Smooth speed display
            hud.vehicle.type = "car"

            local class = GetVehicleClass(veh)
            if (class == 15 or class == 16) then
                hud.vehicle.type = "air"
                hud.vehicle.speed = math.floor(lastSpeed * 0.539957) -- Convert to knots
            end
        else
            lastSpeed = 0
        end

        SendNUIMessage(hud)

        -- Always display the radar
        DisplayRadar(true)

        SetBlipAlpha(GetNorthRadarBlip(), 0)

        Wait(50) -- Frequent updates for smoothness
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(100)
        -- Set a fixed zoom level for the minimap
        SetRadarZoom(1100) -- Adjust as needed
    end
end)

-- HUD COLOUR CHANGER --
local hudSettings = {
    healthColor = "#ffadf0",
    healthBackgroundColor = "#030303",
    armorColor = "#ff40e6"
}
local savedSettings = GetResourceKvpString('hudSettings')
if savedSettings then
    hudSettings = json.decode(savedSettings)
end
RegisterCommand('hud', function()
    local input = lib.inputDialog('Change HUD Colours', {
        {
            type = 'color',
            label = 'Health Bar Color',
            description = 'Pick a new color for the health bar',
            format = 'hex',
            default = hudSettings.healthColor,
            required = true
        },
        {
            type = 'color',
            label = 'Health Bar Background',
            description = 'Pick a new color for the health bar background',
            format = 'hex',
            default = hudSettings.healthBackgroundColor,
            required = true
        },
        {
            type = 'color',
            label = 'Armor Bar Color',
            description = 'Pick a new color for the Armor bar',
            format = 'hex',
            default = hudSettings.armorColor,
            required = false
        }
    })
    if input then
        hudSettings.healthColor = input[1]
        hudSettings.healthBackgroundColor = input[2]
        hudSettings.armorColor = input[3]
        SetResourceKvp('hudSettings', json.encode(hudSettings))
        SendNUIMessage({
            action = "updateHudColors",
            colors = {
                main = hudSettings.healthColor,
                under = hudSettings.healthBackgroundColor,
                armour = hudSettings.armorColor
            }
        })
        lib.notify({
            title = 'Settings Updated',
            type = 'info',
            icon = 'database'
        })
    else
        lib.notify({
            title = 'Cancelled',
            type = 'error',
            icon = 'database'
        })
    end
end)

AddEventHandler('onClientResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        SendNUIMessage({
            action = "updateHudColors",
            colors = {
                main = hudSettings.healthColor,
                under = hudSettings.healthBackgroundColor,
                armour = hudSettings.armorColor
            }
        })
    end
end)
