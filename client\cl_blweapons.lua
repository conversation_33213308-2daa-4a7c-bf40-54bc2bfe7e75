-- OPTIMIZED: Pre-calculate weapon hashes to avoid repeated GetHash<PERSON><PERSON>() calls
local blacklistedWeaponHashes = {}
local blacklistedWeapons = {
    -- Launchers
    "WEAPON_RPG",
    "WEAPON_GRENADELAUNCHER",
    "WEAPON_HOMINGLAUNCHER",
    "WEAPO<PERSON>_COMPACTLAUNCHER",
    "WEAPON_FIREWORK",
    "WEAPON_RAILGUN",
    "WEAPON_RAILGUNXM3",

    -- Explosives
    "WEAPON_GRENADE",
    "WEAPON_STICKYBOMB",
    "WEAPON_PROXMINE",
    "WEAPON_PIPEBOMB",
    "WEAPON_MOLOTOV",
    "WEAPON_SMOKEGRENADE",
    "WEAPON_BZGAS",
    "WEAPON_FLARE",

    -- Snipers
    "WEAPON_HEAVYSNIPER",
    "WEAPON_HEAVYSNIPER_MK2",
    "WEAPON_SNIPERRIFLE",
    "WEAPON_MARKSMANR<PERSON>LE_MK2",
    "WEAPON_PRECISIONRIFLE",

    -- Heavy weapons
    "WEAPON_MINIGUN",

    -- <PERSON>ee troll weapons
    "WEAPON_123",

    -- Others (grief/troll/OP weapons)
    "WEAPON_DIGISCANNER",
    "WEAPON_RAYMINIGUN",
    "WEAPON_RAYCARBINE",
    "WEAPON_RAYPISTOL",
    "WEAPON_CERAMICPISTOL",
    "WEAPON_NAVYREVOLVER",
    "WEAPON_STUNGUN",
    "WEAPON_STUNGUN_MP",
    "weapon_raycarbine",
    "weapon_emplauncher",
    "weapon_railgunxm3",
    "weapon_grenadelauncher_smoke",
    "weapon_firework",
}

-- Pre-calculate all weapon hashes on resource start for better performance
Citizen.CreateThread(function()
    for _, weapon in ipairs(blacklistedWeapons) do
        local hash = GetHashKey(weapon)
        blacklistedWeaponHashes[hash] = true
    end
    print("^2[OPTIMIZATION]^7 Pre-calculated " .. #blacklistedWeapons .. " weapon hashes for blacklist checking")
end)

-- OPTIMIZED: Use pre-calculated hashes and improved logic
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000) -- Increased from 500ms to 1000ms - weapon checks don't need to be super frequent
        local playerPed = PlayerPedId()

        -- Use the pre-calculated hash table for much faster lookups
        for weaponHash, _ in pairs(blacklistedWeaponHashes) do
            if HasPedGotWeapon(playerPed, weaponHash, false) then
                RemoveWeaponFromPed(playerPed, weaponHash)
                TriggerEvent("chatMessage", "X", {190, 0, 0}, "Blacklisted weapon removed!")
                break -- Exit loop early if we found and removed a weapon
            end
        end
    end
end)
