-- Density values from 0.0 to 1.0.
-- OPTIMIZED: Reduced from Wait(0) to Wait(100) to prevent excessive CPU usage
DensityMultiplier = 0.0
Citizen.CreateThread(function()
	while true do
	    Citizen.Wait(100) -- Optimized: Was 0, now 100ms - density doesn't need frame-perfect updates
	    SetVehicleDensityMultiplierThisFrame(DensityMultiplier)
	    SetPedDensityMultiplierThisFrame(DensityMultiplier)
	    SetRandomVehicleDensityMultiplierThisFrame(DensityMultiplier)
	    SetParkedVehicleDensityMultiplierThisFrame(DensityMultiplier)
	    SetScenarioPedDensityMultiplierThisFrame(DensityMultiplier, DensityMultiplier)
	end
end)