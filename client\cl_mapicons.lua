CreateThread(function()
    -- Define a table of blips
    local blips = {
        { coords = vector3(-1328.02, -1054.76, 17.00), sprite = 40, color = 55, scale = 0.7, label = "Mercs ClubHouse" },
        { coords = vector3(-819.51, -717.12, 124.93), sprite = 40, color = 55, scale = 0.7, label = "Little Seoul Records" },
        { coords = vector3(-807.83, -1235.24, 7.34), sprite = 40, color = 55, scale = 0.7, label = "ViceRoy Hospital" },
        { coords = vector3(516.48, -1957.43, 38.76), sprite = 40, color = 55, scale = 0.7, label = "JamesTown ClubHouse" },
        { coords = vector3(73.61, 163.40, 126.4), sprite = 40, color = 55, scale = 0.7, label = "Saints Vinewood" },
        { coords = vector3(-1542.17, -411.82, 60.55), sprite = 40, color = 55, scale = 0.7, label = "Saints Morningwood" },

    }

    -- Loop through each blip and create it
    for _, info in pairs(blips) do
        -- Create the main blip (icon)
        local blip = AddBlipForCoord(info.coords)

        SetBlipSprite(blip, info.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, info.scale) -- <-- Now using custom scale from the table
        SetBlipColour(blip, info.color)
        SetBlipAsShortRange(blip, true)

        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(info.label)
        EndTextCommandSetBlipName(blip)

        -- Create the radius (area blip)
        local radiusBlip = AddBlipForRadius(info.coords, 35.0) -- Radius size in meters
        SetBlipColour(radiusBlip, info.color)
        SetBlipAlpha(radiusBlip, 100) -- 0 = invisible, 255 = fully visible
    end
end)
